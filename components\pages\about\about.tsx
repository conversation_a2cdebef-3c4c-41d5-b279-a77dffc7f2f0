import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON>R<PERSON>, Eye, Heart, Star, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function About() {
   return (
      <>
         <div className="min-h-screen bg-background">
            {/* Hero Section */}
            <section className="pt-36 pb-16 bg-gradient-hero">
               <div className="container mx-auto px-4">
                  <div className="text-center max-w-4xl mx-auto">
                     <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6">
                        About{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Astral Studios
                        </span>
                     </h1>
                     <p className="text-lg text-muted-foreground mb-8 leading-relaxed px-2">
                        Passionate storytellers dedicated to capturing
                        life&apos;s most precious moments. With over 8 years of
                        experience, we specialize in creating timeless memories
                        that you&apos;ll treasure forever.
                     </p>
                     <div className="flex flex-col justify-center items-center sm:flex-row gap-4">
                        <Button asChild size="lg">
                           <Link href="/portfolio">View Our Work</Link>
                        </Button>
                        <Button asChild variant="outline" size="lg">
                           <Link href="/contact">Get in Touch</Link>
                        </Button>
                     </div>
                  </div>
               </div>
            </section>

            {/* Our Story Section */}
            <section className="py-20 bg-astral-grey">
               <div className="container mx-auto px-4">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                     <div className="relative h-120 w-full">
                        <Image
                           src={"/images/img-1.JPG"}
                           alt="Astral Studios Interior"
                           fill
                           className="object-cover object-center
 rounded-3xl shadow-elegant brightness-90"
                        />
                        <div className="absolute -top-6 -left-6 w-32 h-32 bg-gradient-accent rounded-full opacity-20 animate-glow"></div>
                     </div>
                     <div className="space-y-6">
                        <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground">
                           Our{" "}
                           <span className="bg-gradient-accent bg-clip-text text-transparent">
                              Story
                           </span>
                        </h2>
                        <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                           Founded with a deep passion for visual storytelling,
                           Astral Studios emerged from a simple belief: every
                           moment has the potential to become a treasured
                           memory. Our journey began when we realized that
                           photography and videography are more than just
                           capturing images, they&apos;re about preserving
                           emotions, connections, and the authentic beauty of
                           human experiences.
                        </p>
                        <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                           Over the years, we&apos;ve had the privilege of
                           documenting countless love stories, celebrations, and
                           milestones. Each project teaches us something new and
                           reminds us why we fell in love with this craft in the
                           first place. We approach every session with fresh
                           eyes, genuine enthusiasm, and an unwavering
                           commitment to excellence.
                        </p>
                     </div>
                  </div>
               </div>
            </section>

            {/* Our Philosophy Section */}
            <section className="py-20 bg-background">
               <div className="container mx-auto px-4">
                  <div className="text-center mb-16">
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Our{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Philosophy
                        </span>
                     </h2>
                     <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                        At the heart of everything we do are the core values
                        that guide our approach to photography, videography, and
                        client relationships.
                     </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                     <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center">
                        <CardContent className="p-8">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                              <Heart className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                              Storytelling
                           </h3>
                           <p className="text-muted-foreground font-montserrat">
                              Every image tells a story. We focus on capturing
                              authentic moments that speak to the heart and
                              preserve the essence of your special day.
                           </p>
                        </CardContent>
                     </Card>

                     <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center">
                        <CardContent className="p-8">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                              <Eye className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                              Attention to Detail
                           </h3>
                           <p className="text-muted-foreground font-montserrat">
                              From the grandest gestures to the smallest
                              moments, we believe that details matter and every
                              frame deserves careful consideration.
                           </p>
                        </CardContent>
                     </Card>

                     <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center">
                        <CardContent className="p-8">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                              <Star className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                              Excellence
                           </h3>
                           <p className="text-muted-foreground font-montserrat">
                              We strive for excellence in every aspect of our
                              work, from initial consultation to final delivery,
                              ensuring exceptional quality throughout.
                           </p>
                        </CardContent>
                     </Card>

                     <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center">
                        <CardContent className="p-8">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                              <Users className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                              Client-Centric
                           </h3>
                           <p className="text-muted-foreground font-montserrat">
                              Your vision is our priority. We work closely with
                              each client to understand their unique needs and
                              deliver personalized experiences.
                           </p>
                        </CardContent>
                     </Card>
                  </div>
               </div>
            </section>

            {/* Why Choose Us Section */}
            <section className="py-20 bg-astral-grey">
               <div className="container mx-auto px-4">
                  <div className="max-w-4xl mx-auto">
                     <div className="text-center mb-16">
                        <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                           Why Choose{" "}
                           <span className="bg-gradient-accent bg-clip-text text-transparent">
                              Astral Studios?
                           </span>
                        </h2>
                     </div>

                     <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div className="space-y-6">
                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Professional Experience
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    Years of experience capturing weddings,
                                    events, and special moments with technical
                                    expertise and artistic vision.
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Comprehensive Services
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    From photography to videography, 360 video
                                    booths to atmospheric effects, we offer
                                    complete visual solutions.
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Personalized Approach
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    Every client is unique, and we tailor our
                                    services to match your specific vision,
                                    style, and requirements.
                                 </p>
                              </div>
                           </div>
                        </div>

                        <div className="space-y-6">
                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Cutting-Edge Equipment
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    We use the latest professional photography
                                    and videography equipment to ensure the
                                    highest quality results.
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Fast Turnaround
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    We understand your excitement to see your
                                    photos and videos, which is why we
                                    prioritize timely delivery without
                                    compromising quality.
                                 </p>
                              </div>
                           </div>

                           <div className="flex items-start space-x-4">
                              <div className="w-2 h-2 bg-primary rounded-full mt-3 flex-shrink-0"></div>
                              <div>
                                 <h3 className="text-xl font-playfair font-semibold text-foreground mb-2">
                                    Lifetime Memories
                                 </h3>
                                 <p className="text-muted-foreground font-montserrat">
                                    Our goal is to create images and videos that
                                    you&apos;ll treasure for a lifetime, growing
                                    more precious with each passing year.
                                 </p>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </section>

            {/* Call to Action */}
            <section className="py-20 bg-background">
               <div className="container mx-auto px-4 text-center">
                  <div className="max-w-3xl mx-auto">
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                        Let&apos;s Create{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Something Beautiful
                        </span>
                     </h2>
                     <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                        Ready to work with a team that&apos;s as passionate
                        about your special moments as you are? Let&apos;s
                        discuss how we can bring your vision to life.
                     </p>
                     <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Get In Touch{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/portfolio">View Our Work</Link>
                        </Button>
                     </div>
                  </div>
               </div>
            </section>
         </div>
      </>
   );
}
