import { ImageGallery } from "@/components/image-gallery";
import { But<PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Clock, Heart, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG",
      alt: "Pre-wedding couple portrait",
   },
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-2.JPG",
      alt: "Engagement session outdoors",
   },
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-3.JPG",
      alt: "Romantic couple photography",
   },
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-4.JPG",
      alt: "Pre-wedding lifestyle shoot",
   },
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-5.JPG",
      alt: "Couple in natural setting",
   },
   {
      src: "/images/pre-wedding-shoots/pre-wedding-shoot-6.JPG",
      alt: "Engagement photography session",
   },
];

const benefits = [
   {
      icon: Camera,
      title: "Get Comfortable",
      description:
         "Pre-wedding shoots help you get comfortable with your photographer before the big day.",
   },
   {
      icon: Heart,
      title: "Capture Your Love",
      description:
         "Document your relationship and love story in a relaxed, intimate setting.",
   },
   {
      icon: MapPin,
      title: "Meaningful Locations",
      description:
         "Choose locations that are special to your relationship and tell your unique story.",
   },
   {
      icon: Clock,
      title: "No Time Pressure",
      description:
         "Unlike wedding day, we have plenty of time to create the perfect shots without rushing.",
   },
];

export default function PreWeddingPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG"
                  alt="Pre-wedding photography hero"
                  fill
                  className="object-cover object-[50%_25%]"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                  Pre-Wedding Shoots
               </h1>
               <p className="text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
                  Beautiful engagement and pre-wedding photography sessions that
                  capture your love story in stunning locations
               </p>
               <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                  <Button asChild size="lg">
                     <Link href="/contact">Book Your Session</Link>
                  </Button>
                  <Button
                     asChild
                     variant="outline"
                     size="lg"
                     className="text-white hover:bg-white hover:text-black"
                  >
                     <Link href="/portfolio">View Gallery</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Benefits Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Why Choose Pre-Wedding Photography
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Pre-wedding shoots offer the perfect opportunity to capture
                     your love story in a relaxed setting while preparing for
                     your wedding day.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {benefits.map((benefit, index) => (
                     <Card key={index} className="text-center">
                        <CardHeader>
                           <div className="flex justify-center mb-4">
                              <benefit.icon className="h-12 w-12 text-primary" />
                           </div>
                           <CardTitle>{benefit.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription className="text-center">
                              {benefit.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Recent Pre-Wedding Photography
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Browse through our recent pre-wedding and engagement
                     photography sessions to see our style and approach.
                  </p>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                     Ready to Capture Your{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Love Story?{" "}
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                     Let&apos;s create beautiful pre-wedding photos that
                     celebrate your relationship and prepare you for your
                     wedding day.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/contact">
                           Contact Now <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/portfolio">View Portfolio</Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
}
