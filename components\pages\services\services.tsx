"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { featuredServices } from "@/lib/data";
import { motion } from "framer-motion";
import { ArrowR<PERSON>, Check, Sparkles, Video, Wind } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

const Services = () => {
   const [hoveredCard, setHoveredCard] = useState<number | null>(null);

   return (
      <div className="min-h-screen bg-background">
         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4">
               <div className="text-center max-w-4xl mx-auto">
                  <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6">
                     Our{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Services
                     </span>
                  </h1>
                  <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                     From intimate moments to grand celebrations, we offer
                     comprehensive photography and videography services tailored
                     to capture your unique story.
                  </p>
                  <Button asChild size="lg">
                     <Link href="/contact">Book Now</Link>
                  </Button>
               </div>
            </div>
         </section>

         <section className="py-20 bg-astral-grey relative">
            <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
               <motion.div
                  className="text-center mb-16"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
               >
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Photography
                     </span>{" "}
                     Services
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                     Professional photography services tailored to capture the
                     essence and emotion of your most important moments.
                  </p>
               </motion.div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredServices.map((service, index) => {
                     return (
                        <motion.div
                           key={service.title}
                           initial={{ opacity: 0, scale: 0.9 }}
                           whileInView={{ opacity: 1, scale: 1 }}
                           transition={{
                              duration: 0.6,
                              delay: index * 0.1,
                              type: "spring",
                              stiffness: 100,
                           }}
                           viewport={{ once: true }}
                           onHoverStart={() => setHoveredCard(index)}
                           onHoverEnd={() => setHoveredCard(null)}
                           className="group"
                        >
                           <Card className="relative p-0 overflow-hidden bg-card/80 backdrop-blur-sm border-astral-grey-light hover:border-primary/50 transition-all duration-500 h-full">
                              {/* Image with overlay */}
                              <div className="relative aspect-[4/3] overflow-hidden">
                                 <Image
                                    src={service.image}
                                    alt={service.title}
                                    fill
                                    className="object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                                 />

                                 {/* Animated overlay */}
                                 <motion.div
                                    className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"
                                    animate={{
                                       opacity:
                                          hoveredCard === index ? 0.9 : 0.6,
                                    }}
                                    transition={{ duration: 0.3 }}
                                 />
                              </div>

                              <CardContent className="p-6 pt-2">
                                 <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                                    {service.title}
                                 </h3>

                                 <p className="text-muted-foreground text-sm mb-4 leading-relaxed line-clamp-3">
                                    {service.description}
                                 </p>

                                 {/* Features with animated appearance */}
                                 <div className="space-y-2 mb-6">
                                    {service.features
                                       .slice(0, 3)
                                       .map((feature, idx) => (
                                          <motion.div
                                             key={idx}
                                             className="flex items-center gap-2 text-xs text-muted-foreground"
                                             initial={{ opacity: 0, x: -10 }}
                                             animate={{
                                                opacity:
                                                   hoveredCard === index
                                                      ? 1
                                                      : 0.7,
                                                x:
                                                   hoveredCard === index
                                                      ? 0
                                                      : -10,
                                             }}
                                             transition={{
                                                duration: 0.3,
                                                delay: idx * 0.1,
                                             }}
                                          >
                                             <motion.div
                                                className="w-2 h-2 bg-primary rounded-full"
                                                animate={{
                                                   scale:
                                                      hoveredCard === index
                                                         ? 1.2
                                                         : 1,
                                                }}
                                                transition={{ duration: 0.2 }}
                                             />
                                             <span>{feature}</span>
                                          </motion.div>
                                       ))}
                                 </div>

                                 <Button
                                    asChild
                                    size="sm"
                                    className="w-full transition-all duration-300 group-hover:shadow-glow py-3 h-auto"
                                 >
                                    <Link href={service.href}>
                                       <motion.span
                                          animate={{
                                             x: hoveredCard === index ? 5 : 0,
                                          }}
                                          transition={{ duration: 0.2 }}
                                       >
                                          Learn More
                                       </motion.span>
                                       <ArrowRight className="h-4 w-4 ml-2" />
                                    </Link>
                                 </Button>
                              </CardContent>
                           </Card>
                        </motion.div>
                     );
                  })}
               </div>
            </div>
         </section>

         {/* Videography Services */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Videography
                     </span>{" "}
                     Services
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                     Cinematic video production that brings your stories to life
                     with professional quality and creative vision.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center p-0">
                     <CardContent className="p-8">
                        <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                           <Video className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                           Wedding Films
                        </h3>
                        <p className="text-muted-foreground font-montserrat mb-4">
                           Cinematic wedding films capturing the emotion and
                           beauty of your special day.
                        </p>
                        <ul className="text-sm text-muted-foreground font-montserrat space-y-2 mb-6">
                           <li>• Full ceremony coverage</li>
                           <li>• Reception highlights</li>
                           <li>• Professional editing</li>
                           <li>• HD/4K quality</li>
                        </ul>
                     </CardContent>
                  </Card>

                  <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center p-0">
                     <CardContent className="p-8">
                        <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                           <Video className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                           Pre-wedding Videos
                        </h3>
                        <p className="text-muted-foreground font-montserrat mb-4">
                           Romantic pre-wedding video sessions showcasing your
                           love story.
                        </p>
                        <ul className="text-sm text-muted-foreground font-montserrat space-y-2 mb-6">
                           <li>• Multiple locations</li>
                           <li>• Story-driven narrative</li>
                           <li>• Drone footage available</li>
                           <li>• Music synchronization</li>
                        </ul>
                     </CardContent>
                  </Card>

                  <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center p-0">
                     <CardContent className="p-8">
                        <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                           <Video className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                           Event Documentation
                        </h3>
                        <p className="text-muted-foreground font-montserrat mb-4">
                           Professional event videography for special occasions
                           and celebrations.
                        </p>
                        <ul className="text-sm text-muted-foreground font-montserrat space-y-2 mb-6">
                           <li>• Multi-camera setup</li>
                           <li>• Live streaming options</li>
                           <li>• Professional audio</li>
                           <li>• Quick turnaround</li>
                        </ul>
                     </CardContent>
                  </Card>

                  <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group text-center p-0">
                     <CardContent className="p-8">
                        <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6 group-hover:animate-glow transition-all">
                           <Video className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                           Highlight Reels
                        </h3>
                        <p className="text-muted-foreground font-montserrat mb-4">
                           Short, impactful highlight videos perfect for social
                           media sharing.
                        </p>
                        <ul className="text-sm text-muted-foreground font-montserrat space-y-2 mb-6">
                           <li>• 60-90 second duration</li>
                           <li>• Social media optimized</li>
                           <li>• Dynamic editing</li>
                           <li>• Multiple formats</li>
                        </ul>
                     </CardContent>
                  </Card>
               </div>
            </div>
         </section>

         {/* Special Services */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Special
                     </span>{" "}
                     Services
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                     Unique additions to make your event extraordinary and
                     unforgettable.
                  </p>
               </div>

               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  {/* 360 Video Booth */}
                  <Card className="bg-card p-0 border-astral-grey-light hover:shadow-card transition-all">
                     <CardContent className="p-8">
                        <div className="flex items-center space-x-4 mb-6">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                              <Sparkles className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-3xl font-playfair font-bold text-foreground">
                              360 Video Booth
                           </h3>
                        </div>
                        <p className="text-lg text-muted-foreground font-montserrat mb-6 leading-relaxed">
                           Create stunning 360-degree videos that capture your
                           guests from every angle. Our interactive video booth
                           is perfect for weddings, parties, and corporate
                           events, providing entertainment and memorable
                           keepsakes for your guests.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                           <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Professional setup
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    High-quality cameras
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Instant social sharing
                                 </span>
                              </div>
                           </div>
                           <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Custom branding
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Props included
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Professional attendant
                                 </span>
                              </div>
                           </div>
                        </div>
                        <Button
                           asChild
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold"
                        >
                           <Link href="/contact">Book the 360 Booth</Link>
                        </Button>
                     </CardContent>
                  </Card>

                  {/* Dry Ice Machine */}
                  <Card className="bg-card p-0 border-astral-grey-light hover:shadow-card transition-all">
                     <CardContent className="p-8">
                        <div className="flex items-center space-x-4 mb-6">
                           <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                              <Wind className="h-8 w-8 text-white" />
                           </div>
                           <h3 className="text-3xl font-playfair font-bold text-foreground">
                              Dry Ice Machine
                           </h3>
                        </div>
                        <p className="text-lg text-muted-foreground font-montserrat mb-6 leading-relaxed">
                           Add dramatic flair to your event with our
                           professional dry ice machine. Perfect for first
                           dances, grand entrances, and creating magical
                           atmospheric effects that will leave your guests in
                           awe and provide stunning photo opportunities.
                        </p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                           <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Safe operation
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Dramatic effects
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Perfect for first dances
                                 </span>
                              </div>
                           </div>
                           <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Professional grade
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Venue approved
                                 </span>
                              </div>
                              <div className="flex items-center space-x-3">
                                 <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                 <span className="text-muted-foreground font-montserrat">
                                    Stunning visuals
                                 </span>
                              </div>
                           </div>
                        </div>
                        <Button
                           asChild
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold"
                        >
                           <Link href="/contact">Add Dry Ice Effects</Link>
                        </Button>
                     </CardContent>
                  </Card>
               </div>
            </div>
         </section>

         {/* Call to Action */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     Ready to Book{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Your Service?
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                     Let&apos;s discuss your photography and videography needs.
                     We&apos;ll work with you to create a customized package
                     that perfectly captures your special moments.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/contact">
                           Contact Now <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/portfolio">View Portfolio</Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
};

export default Services;
