import { ImageGallery } from "@/components/image-gallery";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Camera, Clock, Heart, Users } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const galleryImages = [
   {
      src: "/images/wedding-shoots/wedding-shoot-1.JPG",
      alt: "Wedding ceremony moment",
   },
   {
      src: "/images/wedding-shoots/wedding-shoot-2.PNG",
      alt: "Bride and groom portrait",
   },
   {
      src: "/images/wedding-shoots/wedding-shoot-3.JPG",
      alt: "Wedding reception",
   },
   {
      src: "/images/wedding-shoots/wedding-shoot-4.JPG",
      alt: "Wedding details",
   },
   { src: "/images/wedding-shoots/wedding-shoot-5.JPG", alt: "Wedding party" },
   { src: "/images/wedding-shoots/wedding-shoot-6.JPG", alt: "Wedding venue" },
];

const features = [
   {
      icon: Heart,
      title: "Romantic Storytelling",
      description:
         "We capture the love story of your special day with artistic vision and emotional depth.",
   },
   {
      icon: Camera,
      title: "Professional Equipment",
      description:
         "State-of-the-art cameras and lenses ensure stunning image quality in any lighting condition.",
   },
   {
      icon: Clock,
      title: "Flexible Coverage",
      description:
         "From intimate ceremonies to grand celebrations, we adapt our coverage to your timeline.",
   },
   {
      icon: Users,
      title: "Experienced Team",
      description:
         "Our skilled photographers have captured hundreds of weddings with professionalism and care.",
   },
];

export default function WeddingPage() {
   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="relative !pt-38 pb-16">
            <div className="absolute inset-0 z-0">
               <Image
                  src="/images/wedding-shoots/wedding-shoot-1.JPG"
                  alt="Wedding photography hero"
                  fill
                  className="object-cover object-top"
                  priority
                  sizes="100vw"
               />
               <div className="absolute inset-0 bg-black/50" />
            </div>

            <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
               <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
                  Wedding Photography
               </h1>
               <p className="text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
                  Capture the magic of your special day with timeless
                  photographs that tell your unique love story
               </p>
               <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Button asChild size="lg">
                     <Link href="/contact">Book Your Wedding</Link>
                  </Button>
                  <Button
                     asChild
                     variant="outline"
                     size="lg"
                     className="text-white hover:bg-white hover:text-black"
                  >
                     <Link href="/portfolio">View Wedding Gallery</Link>
                  </Button>
               </div>
            </div>
         </section>

         {/* Features Section */}
         <section className="py-20">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Why Choose Us for Your Wedding
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     We understand that your wedding day is one of the most
                     important days of your life. Here&apos;s what makes our
                     wedding photography special.
                  </p>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {features.map((feature, index) => (
                     <Card key={index} className="text-center">
                        <CardHeader>
                           <div className="flex justify-center mb-4">
                              <feature.icon className="h-12 w-12 text-primary" />
                           </div>
                           <CardTitle>{feature.title}</CardTitle>
                        </CardHeader>
                        <CardContent>
                           <CardDescription className="text-center">
                              {feature.description}
                           </CardDescription>
                        </CardContent>
                     </Card>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                     Recent Wedding Photography
                  </h2>
                  <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                     Take a look at some of our recent wedding photography work
                     that showcases our style and attention to detail.
                  </p>
               </div>

               <ImageGallery images={galleryImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                     Ready to Book Your{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Wedding Photography?
                     </span>
                  </h2>
                  <p className="text-lg text-muted-foreground font-montserrat mb-8 leading-relaxed">
                     Let&apos;s discuss your wedding day and create a
                     photography package that perfectly captures your love
                     story.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                     <Button
                        asChild
                        size="lg"
                        className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/contact">
                           Contact Now <ArrowRight className="ml-2 h-5 w-5" />
                        </Link>
                     </Button>
                     <Button
                        asChild
                        variant="outline"
                        size="lg"
                        className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                     >
                        <Link href="/portfolio">View Portfolio</Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
}
