"use client";

import {
   Gallery<PERSON>rid,
   GallerySearch,
   LoadMoreButton,
} from "@/components/pages/gallery";
import AlbumCard from "@/components/pages/gallery/album/album-card";
import { StructuredData } from "@/components/structured-data";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { usePublicAlbums, useSearchGalleryItems } from "@/lib/hooks/use-albums";
import { usePublicCollections } from "@/lib/hooks/use-collections";
import { BookmarkSquareIcon, TagIcon } from "@heroicons/react/24/solid";
import { motion } from "framer-motion";
import { ArrowRight, Grid3X3, Tag } from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import CollectionCard from "./collections/collection-card";

const INITIAL_ITEMS_COUNT = 4;

export default function GalleryPageContent() {
   const [searchQuery, setSearchQuery] = useState("");
   const [showMoreAlbums, setShowMoreAlbums] = useState(false);
   const [showMoreCollections, setShowMoreCollections] = useState(false);

   // Fetch data
   const { data: albumsData, isLoading: albumsLoading } = usePublicAlbums({
      limit: showMoreAlbums ? 20 : INITIAL_ITEMS_COUNT,
      sortBy: "createdAt",
      sortOrder: "desc",
   });

   const { data: collectionsData, isLoading: collectionsLoading } =
      usePublicCollections({
         limit: showMoreCollections ? 20 : INITIAL_ITEMS_COUNT,
         sortBy: "createdAt",
         sortOrder: "desc",
      });

   // Search functionality
   const { data: searchResults, isLoading: searchLoading } =
      useSearchGalleryItems(searchQuery, {
         limit: 12,
      });

   // Memoized filtered data
   const {
      displayAlbums,
      displayCollections,
      hasMoreAlbums,
      hasMoreCollections,
   } = useMemo(() => {
      if (searchQuery.trim()) {
         return {
            displayAlbums: searchResults?.albums.data || [],
            displayCollections: searchResults?.collections.data || [],
            hasMoreAlbums: false,
            hasMoreCollections: false,
         };
      }

      const albums = albumsData?.data || [];
      const collections = collectionsData?.data || [];

      return {
         displayAlbums: albums,
         displayCollections: collections,
         hasMoreAlbums:
            albums.length >= INITIAL_ITEMS_COUNT &&
            albumsData?.pagination.hasNext,
         hasMoreCollections:
            collections.length >= INITIAL_ITEMS_COUNT &&
            collectionsData?.pagination.hasNext,
      };
   }, [searchQuery, searchResults, albumsData, collectionsData]);

   const isSearching = searchQuery.trim().length > 0;
   const hasResults = displayAlbums.length > 0 || displayCollections.length > 0;

   return (
      <div className="min-h-screen">
         {/* Structured Data */}
         <StructuredData
            type="imageGallery"
            data={{
               name: "Astral Studios Gallery",
               description:
                  "Professional photography gallery featuring stunning albums and curated collections",
               url: "http://astralstudios.co.uk/gallery",
               albums: displayAlbums.slice(0, 10).map((album) => ({
                  name: album.name,
                  description: album.description,
                  url: `http://astralstudios.co.uk/gallery/albums/${album._id}`,
                  coverImageUrl: album.coverImageUrl,
                  createdAt: album.createdAt,
               })),
            }}
         />

         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4">
               <div className="text-center max-w-4xl mx-auto">
                  <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6">
                     Our{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Gallery
                     </span>
                  </h1>
                  <p className="text-lg text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                     Explore our stunning collection of photography and
                     videography work, organized into beautiful albums and
                     curated collections that showcase the artistry and emotion
                     of every moment we capture.
                  </p>
                  <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                     <Button asChild size="lg">
                        <Link href="/gallery/albums">View All Albums</Link>
                     </Button>
                     <Button asChild size="lg">
                        <Link href="/gallery/collections">
                           View All Collections
                        </Link>
                     </Button>
                  </div>
               </div>
            </div>
         </section>

         {/* Search Section */}
         <section className="pt-12 bg-card/30">
            <div className="container mx-auto px-4">
               <GallerySearch
                  onSearch={setSearchQuery}
                  placeholder="Search albums and collections..."
               />

               {/* Search Results Info */}
               {isSearching && (
                  <div className="text-center mb-8 pt-4">
                     {searchLoading ? (
                        <p className="text-muted-foreground">Searching...</p>
                     ) : hasResults ? (
                        <p className="text-muted-foreground">
                           Found{" "}
                           {displayAlbums.length + displayCollections.length}{" "}
                           results for &quot;
                           <span className="text-foreground font-medium">
                              {searchQuery}
                           </span>
                           &quot;
                        </p>
                     ) : (
                        <p className="text-muted-foreground">
                           No results found for &quot;
                           <span className="text-foreground font-medium">
                              {searchQuery}
                           </span>
                           &quot;
                        </p>
                     )}
                  </div>
               )}
            </div>
         </section>

         {/* Main Content */}
         <section className="py-12">
            <div className="container mx-auto px-4">
               {/* Albums Section */}
               <div className="mb-12">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-12">
                     <div>
                        <motion.h2
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           transition={{ duration: 0.6 }}
                           className="text-2xl md:text-3xl font-bold text-foreground mb-4 flex items-center gap-3"
                        >
                           <BookmarkSquareIcon className="w-8 h-8 text-primary" />
                           Albums
                        </motion.h2>
                        <motion.p
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           transition={{ duration: 0.6, delay: 0.1 }}
                           className="text-muted-foreground"
                        >
                           {isSearching ? (
                              <>{displayAlbums.length} albums found</>
                           ) : (
                              <span className="hidden sm:inline">
                                 Discover our most recent photo albums
                              </span>
                           )}
                        </motion.p>
                     </div>

                     {!isSearching && (
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="mt-2 sm:mt-0"
                        >
                           <Link
                              href="/gallery/albums"
                              className="flex items-center gap-2"
                           >
                              View All Albums
                              <ArrowRight className="w-4 h-4" />
                           </Link>
                        </Button>
                     )}
                  </div>

                  {/* Albums Grid */}
                  {albumsLoading && !isSearching ? (
                     <GalleryGrid columns={4} className="mb-8">
                        {Array.from({ length: 4 }).map((_, i) => (
                           <div key={i} className="space-y-4">
                              <Skeleton className="aspect-[4/3] rounded-2xl" />
                              <Skeleton className="h-6 w-3/4" />
                              <Skeleton className="h-4 w-1/2" />
                           </div>
                        ))}
                     </GalleryGrid>
                  ) : displayAlbums.length > 0 ? (
                     <>
                        <GalleryGrid columns={4} gap="lg" className="mb-8">
                           {displayAlbums.map((album) => (
                              <AlbumCard
                                 key={String(album._id)}
                                 album={album}
                                 variant="default"
                                 showStats={true}
                                 showDescription={false}
                              />
                           ))}
                        </GalleryGrid>

                        {/* Load More Albums */}
                        {!isSearching && hasMoreAlbums && !showMoreAlbums && (
                           <LoadMoreButton
                              onClick={() => setShowMoreAlbums(true)}
                              buttonText="View More Albums"
                           />
                        )}
                     </>
                  ) : (
                     !albumsLoading && (
                        <div className="text-center py-12">
                           <Grid3X3 className="w-16 h-16 mx-auto mb-4 text-muted-foreground/50" />
                           <p className="text-muted-foreground text-lg">
                              {isSearching
                                 ? "No albums found"
                                 : "No albums available"}
                           </p>
                        </div>
                     )
                  )}
               </div>

               {/* Collections Section */}
               <div>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-12">
                     <div>
                        <motion.h2
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           transition={{ duration: 0.6, delay: 0.2 }}
                           className="text-2xl md:text-3xl font-bold text-foreground mb-4 flex items-center gap-3"
                        >
                           <TagIcon className="size-8 text-primary" />
                           Collections
                        </motion.h2>
                        <motion.p
                           initial={{ opacity: 0, x: -20 }}
                           animate={{ opacity: 1, x: 0 }}
                           transition={{ duration: 0.6, delay: 0.3 }}
                           className="text-muted-foreground"
                        >
                           {isSearching ? (
                              <>{displayCollections.length} collections found</>
                           ) : (
                              <span className="hidden sm:inline">
                                 Explore curated collections of event
                                 photography and memorable moments
                              </span>
                           )}
                        </motion.p>
                     </div>

                     {!isSearching && (
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="mt-2 sm:mt-0"
                        >
                           <Link
                              href="/gallery/collections"
                              className="flex items-center gap-2"
                           >
                              View All Collections
                              <ArrowRight className="w-4 h-4" />
                           </Link>
                        </Button>
                     )}
                  </div>

                  {/* Collections Grid */}
                  {collectionsLoading && !isSearching ? (
                     <GalleryGrid columns={3} className="mb-8">
                        {Array.from({ length: 3 }).map((_, i) => (
                           <div key={i} className="space-y-4">
                              <Skeleton className="h-48 w-full rounded-3xl" />
                           </div>
                        ))}
                     </GalleryGrid>
                  ) : displayCollections.length > 0 ? (
                     <>
                        <GalleryGrid columns={3} className="mb-8">
                           {displayCollections.map((collection) => (
                              <CollectionCard
                                 key={String(collection._id)}
                                 collection={collection}
                                 variant="default"
                                 showStats={true}
                                 showDescription={true}
                              />
                           ))}
                        </GalleryGrid>

                        {/* Load More Collections */}
                        {!isSearching &&
                           hasMoreCollections &&
                           !showMoreCollections && (
                              <LoadMoreButton
                                 onClick={() => setShowMoreCollections(true)}
                                 buttonText="View More Collections"
                              />
                           )}
                     </>
                  ) : (
                     !collectionsLoading && (
                        <div className="text-center py-12">
                           <Tag className="w-16 h-16 mx-auto mb-4 text-muted-foreground/50" />
                           <p className="text-muted-foreground text-lg">
                              {isSearching
                                 ? "No collections found"
                                 : "No collections available"}
                           </p>
                        </div>
                     )
                  )}
               </div>
            </div>
         </section>
      </div>
   );
}
