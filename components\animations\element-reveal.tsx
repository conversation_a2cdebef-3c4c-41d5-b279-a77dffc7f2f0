"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

interface Props {
   children: React.ReactNode;
   delay?: number;
   className?: string;
}

export default function ElementReveal({
   children,
   delay = 0.6,
   className,
}: Props) {
   return (
      <div className="overflow-hidden">
         <motion.div
            initial={{ opacity: 0, y: "100%" }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: delay }}
            viewport={{ once: true }}
            className={cn("w-full", className)}
         >
            {children}
         </motion.div>
      </div>
   );
}
