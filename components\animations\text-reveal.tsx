"use client";

import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import { SplitText } from "gsap/dist/SplitText";
import React, { useRef } from "react";

gsap.registerPlugin(ScrollTrigger, SplitText);

type Props = {
   children: React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;
   animateOnScroll?: boolean;
   delay?: number;
   className?: string;
};

export default function TextReveal({
   children,
   animateOnScroll = true,
   delay = 0,
   className,
}: Props) {
   const containerRef = useRef<HTMLDivElement | null>(null);
   const elementRef = useRef<HTMLElement[]>([]);
   const splitRef = useRef<SplitText[]>([]);
   const lines = useRef<HTMLElement[]>([]);

   useGSAP(
      () => {
         if (!containerRef.current) return;

         splitRef.current = [];
         elementRef.current = [];
         lines.current = [];

         let elements = [];
         if (containerRef.current.hasAttribute("data-copy-wrapper")) {
            elements = Array.from(containerRef.current.children);
         } else {
            elements = [containerRef.current];
         }

         elements.forEach((element) => {
            elementRef.current.push(element as HTMLElement);

            const split = SplitText.create(element, {
               type: "lines",
               mask: "lines",
               linesClass: "line++",
            });

            splitRef.current.push(split);

            const computedStyle = window.getComputedStyle(element);
            const textIndent = computedStyle.textIndent;

            if (textIndent && textIndent !== "0px") {
               if (split.lines.length > 0) {
                  (split.lines[0] as HTMLElement).style.paddingLeft =
                     textIndent;
               }

               (element as HTMLElement).style.textIndent = "0";
            }

            lines.current.push(...(split.lines as HTMLElement[]));
         });

         gsap.set(lines.current, { y: "100%" });

         const animationProps = {
            y: 0,
            duration: 1,
            stagger: 0.1,
            ease: "power4.out",
            delay: delay,
         };

         if (animateOnScroll) {
            gsap.to(lines.current, {
               ...animationProps,
               scrollTrigger: {
                  trigger: containerRef.current,
                  start: "top 75%",
                  once: false,
               },
            });
         } else {
            gsap.to(lines.current, animationProps);
         }

         return () => {
            splitRef.current.forEach((split) => {
               if (split) {
                  split.revert();
               }
            });
         };
      },
      {
         scope: containerRef,
         dependencies: [animateOnScroll, delay],
      }
   );

   if (React.Children.count(children) === 1 && React.isValidElement(children)) {
      return React.createElement(
         "div",
         {
            ref: containerRef as React.RefObject<HTMLDivElement>,
            className: className,
         },
         children
      );
   }

   return (
      <div ref={containerRef} data-copy-wrapper="true" className={className}>
         {children}
      </div>
   );
}
