"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import SocialMediaTag, { SocialMediaVariant } from "@/components/social-media-tag";

const variants: { name: SocialMediaVariant; label: string; description: string }[] = [
   {
      name: "floating",
      label: "Floating Glass",
      description: "Modern glassmorphism design with backdrop blur and floating effect"
   },
   {
      name: "minimal",
      label: "Minimal Dark",
      description: "Clean and simple dark design with subtle hover effects"
   },
   {
      name: "gradient",
      label: "Gradient Accent",
      description: "Colorful gradient background matching the brand accent colors"
   },
   {
      name: "neon",
      label: "Neon Glow",
      description: "Cyberpunk-inspired design with glowing cyan borders and effects"
   },
   {
      name: "elegant",
      label: "Elegant Muted",
      description: "Sophisticated design using the brand's grey palette with subtle borders"
   },
];

interface SocialMediaVariantSelectorProps {
   onVariantChange?: (variant: SocialMediaVariant) => void;
   className?: string;
}

export function SocialMediaVariantSelector({ 
   onVariantChange, 
   className 
}: SocialMediaVariantSelectorProps) {
   const [selectedVariant, setSelectedVariant] = useState<SocialMediaVariant>("floating");

   const handleVariantChange = (variant: SocialMediaVariant) => {
      setSelectedVariant(variant);
      onVariantChange?.(variant);
   };

   return (
      <div className={className}>
         <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
               <CardTitle className="text-center">Social Media Component Variants</CardTitle>
               <p className="text-center text-muted-foreground">
                  Choose a variant to preview the social media component styling
               </p>
            </CardHeader>
            <CardContent className="space-y-4">
               <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {variants.map((variant) => (
                     <Button
                        key={variant.name}
                        variant={selectedVariant === variant.name ? "default" : "outline"}
                        onClick={() => handleVariantChange(variant.name)}
                        className="h-auto p-3 flex flex-col items-start text-left"
                     >
                        <span className="font-semibold">{variant.label}</span>
                        <span className="text-xs text-muted-foreground mt-1 leading-tight">
                           {variant.description}
                        </span>
                     </Button>
                  ))}
               </div>
               
               <div className="mt-6 p-4 bg-muted rounded-lg">
                  <h4 className="font-semibold mb-2">Current Selection: {variants.find(v => v.name === selectedVariant)?.label}</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                     {variants.find(v => v.name === selectedVariant)?.description}
                  </p>
                  <p className="text-xs text-muted-foreground">
                     To use this variant, update the <code className="bg-background px-1 rounded">socialMediaVariant</code> 
                     variable in the HeroSection component to <code className="bg-background px-1 rounded">"{selectedVariant}"</code>
                  </p>
               </div>
            </CardContent>
         </Card>

         {/* Preview Component */}
         <SocialMediaTag variant={selectedVariant} />
      </div>
   );
}

export default SocialMediaVariantSelector;
