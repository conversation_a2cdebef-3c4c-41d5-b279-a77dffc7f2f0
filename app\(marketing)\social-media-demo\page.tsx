import SocialMediaVariantSelector from "@/components/social-media-variant-selector";

export default function SocialMediaDemoPage() {
   return (
      <div className="min-h-screen pt-32 pb-16 bg-gradient-to-br from-background to-astral-grey">
         <div className="container mx-auto px-4">
            <div className="text-center mb-12">
               <h1 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-4">
                  Social Media Component{" "}
                  <span className="bg-gradient-accent bg-clip-text text-transparent">
                     Variants
                  </span>
               </h1>
               <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Preview and select from 5 different visual variants of the social media tag component.
                  Each variant offers a unique styling approach to match different design preferences.
               </p>
            </div>

            <SocialMediaVariantSelector className="relative z-10" />

            <div className="mt-16 max-w-4xl mx-auto">
               <h2 className="text-2xl font-playfair font-bold text-foreground mb-6 text-center">
                  Implementation Guide
               </h2>
               
               <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-card p-6 rounded-lg border">
                     <h3 className="font-semibold mb-3">Basic Usage</h3>
                     <pre className="bg-muted p-3 rounded text-sm overflow-x-auto">
{`import SocialMediaTag from "@/components/social-media-tag";

// Default floating variant
<SocialMediaTag />

// Specific variant
<SocialMediaTag variant="elegant" />`}
                     </pre>
                  </div>

                  <div className="bg-card p-6 rounded-lg border">
                     <h3 className="font-semibold mb-3">Available Variants</h3>
                     <ul className="text-sm space-y-1">
                        <li><code className="bg-muted px-1 rounded">floating</code> - Glass morphism design</li>
                        <li><code className="bg-muted px-1 rounded">minimal</code> - Clean dark design</li>
                        <li><code className="bg-muted px-1 rounded">gradient</code> - Brand gradient colors</li>
                        <li><code className="bg-muted px-1 rounded">neon</code> - Cyberpunk glow effect</li>
                        <li><code className="bg-muted px-1 rounded">elegant</code> - Sophisticated muted</li>
                     </ul>
                  </div>
               </div>

               <div className="mt-6 bg-card p-6 rounded-lg border">
                  <h3 className="font-semibold mb-3">Features</h3>
                  <ul className="text-sm space-y-2">
                     <li>• <strong>Responsive Design:</strong> Adapts to different screen sizes</li>
                     <li>• <strong>Accessibility:</strong> Proper ARIA labels and keyboard navigation</li>
                     <li>• <strong>Smooth Animations:</strong> Entrance and hover animations using Framer Motion</li>
                     <li>• <strong>Social Links:</strong> Instagram, TikTok, and Gmail integration</li>
                     <li>• <strong>Fixed Positioning:</strong> Stays in the right center of the viewport</li>
                     <li>• <strong>Rotated Design:</strong> Tilted to the left for visual interest</li>
                  </ul>
               </div>
            </div>
         </div>
      </div>
   );
}
