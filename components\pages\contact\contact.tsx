"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Mail, MapPin, MessageCircle, Phone, Send } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

const Contact = () => {
   const [formData, setFormData] = useState({
      name: "",
      email: "",
      phone: "",
      service: "",
      eventDate: "",
      message: "",
   });

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      // Create mailto link with form data
      const subject = `Photography Inquiry - ${formData.service}`;
      const body = `
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Service: ${formData.service}
Event Date: ${formData.eventDate}

Message:
${formData.message}
    `;

      const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(
         subject
      )}&body=${encodeURIComponent(body)}`;
      window.location.href = mailtoLink;

      toast("Thank you for your inquiry!", {
         description:
            "Your email client will open with your message. We'll get back to you soon!",
      });
   };

   const handleInputChange = (field: string, value: string) => {
      setFormData((prev) => ({
         ...prev,
         [field]: value,
      }));
   };

   return (
      <div className="min-h-screen bg-background">
         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-6">
               <div className="text-center max-w-4xl mx-auto">
                  <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6">
                     Get In{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Touch
                     </span>
                  </h1>
                  <p className="text-xl text-muted-foreground font-montserrat leading-relaxed">
                     Ready to capture your special moments? Let&apos;s discuss
                     your photography and videography needs.
                  </p>
               </div>
            </div>
         </section>

         {/* Contact Content */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-6">
               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  {/* Contact Form */}
                  <Card className="bg-card p-0 border-astral-grey-light shadow-card">
                     <CardContent className="p-8">
                        <h2 className="text-3xl font-playfair font-bold text-foreground mb-6">
                           Send Us a Message
                        </h2>
                        <form onSubmit={handleSubmit} className="space-y-6">
                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                 <Label
                                    htmlFor="name"
                                    className="font-montserrat font-medium"
                                 >
                                    Name *
                                 </Label>
                                 <Input
                                    id="name"
                                    type="text"
                                    required
                                    value={formData.name}
                                    onChange={(e) =>
                                       handleInputChange("name", e.target.value)
                                    }
                                    className="font-montserrat"
                                    placeholder="Your full name"
                                 />
                              </div>
                              <div className="space-y-2">
                                 <Label
                                    htmlFor="email"
                                    className="font-montserrat font-medium"
                                 >
                                    Email *
                                 </Label>
                                 <Input
                                    id="email"
                                    type="email"
                                    required
                                    value={formData.email}
                                    onChange={(e) =>
                                       handleInputChange(
                                          "email",
                                          e.target.value
                                       )
                                    }
                                    className="font-montserrat"
                                    placeholder="<EMAIL>"
                                 />
                              </div>
                           </div>

                           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                 <Label
                                    htmlFor="phone"
                                    className="font-montserrat font-medium"
                                 >
                                    Phone
                                 </Label>
                                 <Input
                                    id="phone"
                                    type="tel"
                                    value={formData.phone}
                                    onChange={(e) =>
                                       handleInputChange(
                                          "phone",
                                          e.target.value
                                       )
                                    }
                                    className="font-montserrat"
                                    placeholder="+44 7XXX XXXXXX"
                                 />
                              </div>
                              <div className="space-y-2">
                                 <Label
                                    htmlFor="eventDate"
                                    className="font-montserrat font-medium"
                                 >
                                    Event Date
                                 </Label>
                                 <Input
                                    id="eventDate"
                                    type="date"
                                    value={formData.eventDate}
                                    onChange={(e) =>
                                       handleInputChange(
                                          "eventDate",
                                          e.target.value
                                       )
                                    }
                                    className="font-montserrat"
                                 />
                              </div>
                           </div>

                           <div className="space-y-2">
                              <Label
                                 htmlFor="service"
                                 className="font-montserrat font-medium"
                              >
                                 Service Interested In *
                              </Label>
                              <Select
                                 onValueChange={(value) =>
                                    handleInputChange("service", value)
                                 }
                                 required
                              >
                                 <SelectTrigger className="font-montserrat">
                                    <SelectValue placeholder="Select a service" />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value="wedding-photography">
                                       Wedding Photography
                                    </SelectItem>
                                    <SelectItem value="pre-wedding">
                                       Pre-wedding Shoots
                                    </SelectItem>
                                    <SelectItem value="pregnancy">
                                       Pregnancy Photography
                                    </SelectItem>
                                    <SelectItem value="child-dedication">
                                       Child Dedication
                                    </SelectItem>
                                    <SelectItem value="videography">
                                       Videography
                                    </SelectItem>
                                    <SelectItem value="360-booth">
                                       360 Video Booth
                                    </SelectItem>
                                    <SelectItem value="dry-ice">
                                       Dry Ice Machine
                                    </SelectItem>
                                    <SelectItem value="package">
                                       Custom Package
                                    </SelectItem>
                                    <SelectItem value="other">Other</SelectItem>
                                 </SelectContent>
                              </Select>
                           </div>

                           <div className="space-y-2">
                              <Label
                                 htmlFor="message"
                                 className="font-montserrat font-medium"
                              >
                                 Message *
                              </Label>
                              <Textarea
                                 id="message"
                                 required
                                 value={formData.message}
                                 onChange={(e) =>
                                    handleInputChange("message", e.target.value)
                                 }
                                 className="font-montserrat min-h-32"
                                 placeholder="Tell us about your event, vision, and any specific requirements..."
                              />
                           </div>

                           <Button
                              type="submit"
                              size="lg"
                              className="w-full bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg"
                           >
                              Send Message <Send className="ml-2 h-5 w-5" />
                           </Button>
                        </form>
                     </CardContent>
                  </Card>

                  {/* Contact Information */}
                  <div className="space-y-8">
                     <div>
                        <h2 className="text-3xl font-playfair font-bold text-foreground mb-6">
                           Contact Information
                        </h2>
                        <p className="text-lg text-muted-foreground font-montserrat leading-relaxed mb-8">
                           We&apos;d love to hear from you! Reach out through
                           any of the following channels, and we&apos;ll get
                           back to you as soon as possible.
                        </p>
                     </div>

                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group">
                           <CardContent className="p-6">
                              <div className="flex items-center space-x-4">
                                 <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:animate-glow transition-all">
                                    <Mail className="h-6 w-6 text-white" />
                                 </div>
                                 <div>
                                    <h3 className="font-playfair font-semibold text-foreground mb-1">
                                       Email
                                    </h3>
                                    <a
                                       href="mailto:<EMAIL>"
                                       className="text-muted-foreground hover:text-primary transition-colors font-montserrat"
                                    >
                                       <EMAIL>
                                    </a>
                                 </div>
                              </div>
                           </CardContent>
                        </Card>

                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group">
                           <CardContent className="p-6">
                              <div className="flex items-center space-x-4">
                                 <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:animate-glow transition-all">
                                    <Phone className="h-6 w-6 text-white" />
                                 </div>
                                 <div>
                                    <h3 className="font-playfair font-semibold text-foreground mb-1">
                                       Phone
                                    </h3>
                                    <a
                                       href="tel:+447886161245"
                                       className="text-muted-foreground hover:text-primary transition-colors font-montserrat"
                                    >
                                       +44 7886 161245
                                    </a>
                                 </div>
                              </div>
                           </CardContent>
                        </Card>

                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group">
                           <CardContent className="p-6">
                              <div className="flex items-center space-x-4">
                                 <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:animate-glow transition-all">
                                    <MessageCircle className="h-6 w-6 text-white" />
                                 </div>
                                 <div>
                                    <h3 className="font-playfair font-semibold text-foreground mb-1">
                                       WhatsApp
                                    </h3>
                                    <a
                                       href="https://wa.me/447865000828"
                                       className="text-muted-foreground hover:text-primary transition-colors font-montserrat"
                                    >
                                       07865 000828
                                    </a>
                                 </div>
                              </div>
                           </CardContent>
                        </Card>

                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all group">
                           <CardContent className="p-6">
                              <div className="flex items-center space-x-4">
                                 <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:animate-glow transition-all">
                                    <MapPin className="h-6 w-6 text-white" />
                                 </div>
                                 <div>
                                    <h3 className="font-playfair font-semibold text-foreground mb-1">
                                       Location
                                    </h3>
                                    <p className="text-muted-foreground font-montserrat">
                                       United Kingdom
                                    </p>
                                 </div>
                              </div>
                           </CardContent>
                        </Card>
                     </div>

                     {/* Social Media */}
                     <div>
                        <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                           Follow Us
                        </h3>
                        <div className="flex space-x-4">
                           <a
                              href="https://instagram.com/astralstudios"
                              className="p-3 bg-astral-grey rounded-full hover:bg-primary transition-all group"
                           >
                              <svg
                                 xmlns="http://www.w3.org/2000/svg"
                                 viewBox="0 0 640 640"
                                 className="h-7 w-7 fill-white"
                              >
                                 <path d="M320.3 205C256.8 204.8 205.2 256.2 205 319.7C204.8 383.2 256.2 434.8 319.7 435C383.2 435.2 434.8 383.8 435 320.3C435.2 256.8 383.8 205.2 320.3 205zM319.7 245.4C360.9 245.2 394.4 278.5 394.6 319.7C394.8 360.9 361.5 394.4 320.3 394.6C279.1 394.8 245.6 361.5 245.4 320.3C245.2 279.1 278.5 245.6 319.7 245.4zM413.1 200.3C413.1 185.5 425.1 173.5 439.9 173.5C454.7 173.5 466.7 185.5 466.7 200.3C466.7 215.1 454.7 227.1 439.9 227.1C425.1 227.1 413.1 215.1 413.1 200.3zM542.8 227.5C541.1 191.6 532.9 159.8 506.6 133.6C480.4 107.4 448.6 99.2 412.7 97.4C375.7 95.3 264.8 95.3 227.8 97.4C192 99.1 160.2 107.3 133.9 133.5C107.6 159.7 99.5 191.5 97.7 227.4C95.6 264.4 95.6 375.3 97.7 412.3C99.4 448.2 107.6 480 133.9 506.2C160.2 532.4 191.9 540.6 227.8 542.4C264.8 544.5 375.7 544.5 412.7 542.4C448.6 540.7 480.4 532.5 506.6 506.2C532.8 480 541 448.2 542.8 412.3C544.9 375.3 544.9 264.5 542.8 227.5zM495 452C487.2 471.6 472.1 486.7 452.4 494.6C422.9 506.3 352.9 503.6 320.3 503.6C287.7 503.6 217.6 506.2 188.2 494.6C168.6 486.8 153.5 471.7 145.6 452C133.9 422.5 136.6 352.5 136.6 319.9C136.6 287.3 134 217.2 145.6 187.8C153.4 168.2 168.5 153.1 188.2 145.2C217.7 133.5 287.7 136.2 320.3 136.2C352.9 136.2 423 133.6 452.4 145.2C472 153 487.1 168.1 495 187.8C506.7 217.3 504 287.3 504 319.9C504 352.5 506.7 422.6 495 452z" />
                              </svg>{" "}
                           </a>
                           <a
                              href="https://tiktok.com/@astralstudioz"
                              className="p-3 bg-astral-grey rounded-full hover:bg-primary transition-all group"
                           >
                              <svg
                                 xmlns="http://www.w3.org/2000/svg"
                                 viewBox="0 0 640 640"
                                 className="h-7 w-7 fill-white"
                              >
                                 <path d="M544.5 273.9C500.5 274 457.5 260.3 421.7 234.7L421.7 413.4C421.7 446.5 411.6 478.8 392.7 506C373.8 533.2 347.1 554 316.1 565.6C285.1 577.2 251.3 579.1 219.2 570.9C187.1 562.7 158.3 545 136.5 520.1C114.7 495.2 101.2 464.1 97.5 431.2C93.8 398.3 100.4 365.1 116.1 336C131.8 306.9 156.1 283.3 185.7 268.3C215.3 253.3 248.6 247.8 281.4 252.3L281.4 342.2C266.4 337.5 250.3 337.6 235.4 342.6C220.5 347.6 207.5 357.2 198.4 369.9C189.3 382.6 184.4 398 184.5 413.8C184.6 429.6 189.7 444.8 199 457.5C208.3 470.2 221.4 479.6 236.4 484.4C251.4 489.2 267.5 489.2 282.4 484.3C297.3 479.4 310.4 469.9 319.6 457.2C328.8 444.5 333.8 429.1 333.8 413.4L333.8 64L421.8 64C421.7 71.4 422.4 78.9 423.7 86.2C426.8 102.5 433.1 118.1 442.4 131.9C451.7 145.7 463.7 157.5 477.6 166.5C497.5 179.6 520.8 186.6 544.6 186.6L544.6 274z" />
                              </svg>{" "}
                           </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </section>

         {/* FAQ Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-6">
               <div className="text-center mb-16">
                  <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6">
                     Frequently Asked{" "}
                     <span className="bg-gradient-accent bg-clip-text text-transparent">
                        Questions
                     </span>
                  </h2>
               </div>

               <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-6">
                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           How far in advance should I book?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           We recommend reserving your wedding date 2-4 weeks in
                           advance, especially during peak season. For other
                           services, a few weeks notice is usually sufficient
                        </p>
                     </div>

                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           Do you travel for destination events?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           Yes! We love destination weddings and events. Travel
                           fees may apply depending on the location. Contact us
                           to discuss your specific needs.
                        </p>
                     </div>

                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           What&apos;s included in your packages?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           Each package is customized to your needs. Generally
                           includes professional photography/videography,
                           editing, digital gallery, and consultation. Specific
                           details vary by service.
                        </p>
                     </div>
                  </div>

                  <div className="space-y-6">
                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           How long until we receive our photos?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           Preview images are typically delivered within 48-72
                           hours. Full galleries are delivered within 2-4 weeks
                           for weddings, 1-2 weeks for other sessions.
                        </p>
                     </div>

                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           Can we request specific shots?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           Absolutely! We encourage you to share your vision and
                           any specific shots you&apos;d like. We&apos;ll work
                           together to create a shot list that captures your
                           priorities.
                        </p>
                     </div>

                     <div>
                        <h3 className="text-lg font-playfair font-semibold text-foreground mb-2">
                           Do you offer payment plans?
                        </h3>
                        <p className="text-muted-foreground font-montserrat">
                           Yes, we offer flexible payment plans to make our
                           services accessible. Contact us to discuss options
                           that work for your budget.
                        </p>
                     </div>
                  </div>
               </div>
            </div>
         </section>
      </div>
   );
};

export default Contact;
